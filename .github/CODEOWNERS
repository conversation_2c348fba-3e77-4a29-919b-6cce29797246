# Global code owners
* @project-lead @tech-lead

# React Native specific files
*.tsx @frontend-team @react-native-expert
*.ts @frontend-team @react-native-expert
*.js @frontend-team
*.jsx @frontend-team

# Native iOS files
ios/ @ios-team @native-expert
*.swift @ios-team
*.m @ios-team
*.h @ios-team
Podfile @ios-team
*.podspec @ios-team

# Native Android files
android/ @android-team @native-expert
*.java @android-team
*.kt @android-team
*.gradle @android-team
gradle.properties @android-team

# Configuration files
package.json @tech-lead @devops-team
pnpm-lock.yaml @tech-lead @devops-team
tsconfig.json @tech-lead @frontend-team
babel.config.js @tech-lead @frontend-team
metro.config.js @tech-lead @frontend-team

# CI/CD and DevOps
.github/ @devops-team @tech-lead
.github/workflows/ @devops-team
Dockerfile @devops-team
docker-compose.yml @devops-team

# Documentation
README.md @project-lead @tech-lead
docs/ @project-lead @tech-lead
.kiro/specs/ @project-lead @tech-lead

# Security and sensitive files
.env* @tech-lead @security-team
*.key @tech-lead @security-team
*.pem @tech-lead @security-team

# AI and ML related files
src/services/ai/ @ai-team @tech-lead
src/services/tongyi/ @ai-team

# Payment integration
src/services/payment/ @payment-team @security-team
src/services/wechat/ @payment-team
src/services/alipay/ @payment-team

# Database and API
src/services/api/ @backend-team @tech-lead
src/types/api.ts @backend-team @frontend-team

# Testing files
__tests__/ @qa-team @tech-lead
*.test.ts @qa-team @frontend-team
*.test.tsx @qa-team @frontend-team
jest.config.js @qa-team @tech-lead

# Performance critical files
src/utils/performance/ @performance-team @tech-lead
src/hooks/usePerformance.ts @performance-team