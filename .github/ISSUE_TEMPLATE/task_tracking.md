---
name: 任务跟踪 (Task Tracking)
about: 跟踪开发任务的进度和问题
title: '[Task-{number}] '
labels: 'task'
assignees: ''
---

## 任务信息
- **任务编号**: Task-{number}
- **任务标题**: 
- **所属阶段**: 第{x}周
- **分支名称**: `feature/task-{number}-{description}`
- **负责人**: @{username}

## 任务描述
请详细描述任务的具体内容和目标。

## 验收标准
请列出任务完成的验收标准：
- [ ] 
- [ ] 
- [ ] 

## 技术要求
- [ ] 功能实现完成
- [ ] 单元测试覆盖率>80%
- [ ] 代码规范检查通过
- [ ] 真机测试通过
- [ ] 文档更新完成

## 依赖关系
### 前置任务
- [ ] Task-{number}: {description}

### 后续任务
- [ ] Task-{number}: {description}

## 开发进度
- [ ] 需求分析 (0%)
- [ ] 技术设计 (0%)
- [ ] 代码实现 (0%)
- [ ] 单元测试 (0%)
- [ ] 集成测试 (0%)
- [ ] 代码审查 (0%)
- [ ] 文档更新 (0%)
- [ ] 任务完成 (0%)

## 遇到的问题
请记录开发过程中遇到的问题：

### 技术问题
- 

### 依赖问题
- 

### 其他问题
- 

## 解决方案
请记录问题的解决方案：

## 时间估算
- **预估时间**: {x}天
- **实际时间**: {x}天
- **完成日期**: YYYY-MM-DD

## 测试说明
### 测试用例
- 

### 测试结果
- [ ] iOS测试通过
- [ ] Android测试通过
- [ ] 单元测试通过
- [ ] 集成测试通过

## 相关资源
- 设计稿: 
- API文档: 
- 参考资料: 

## 备注
请添加任何其他相关信息。