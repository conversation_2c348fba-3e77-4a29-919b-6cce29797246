---
name: Bug报告 (Bug Report)
about: 创建报告以帮助我们改进
title: '[BUG] '
labels: 'bug'
assignees: ''
---

## Bug描述
请清楚简洁地描述bug是什么。

## 复现步骤
重现行为的步骤：
1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## 预期行为
请清楚简洁地描述您期望发生的事情。

## 实际行为
请描述实际发生的事情。

## 截图/录屏
如果适用，请添加截图或录屏来帮助解释您的问题。

## 环境信息
请完成以下信息：

### 设备信息
- 设备: [例如 iPhone 14, Samsung Galaxy S23]
- 操作系统: [例如 iOS 16.1, Android 13]
- 应用版本: [例如 1.0.0]

### 开发环境 (如果是开发相关)
- Node.js版本: [例如 18.17.0]
- React Native版本: [例如 0.75.0]
- 开发工具: [例如 Xcode 15.0, Android Studio 2023.1]

## 错误日志
如果有相关的错误日志，请粘贴在这里：

```
请在此处粘贴错误日志
```

## 相关任务
- [ ] 是否与特定任务相关？如是，请列出：Task-{number}
- [ ] 是否在特定分支上发现？分支名：

## 严重程度
- [ ] 严重 (Critical) - 应用崩溃或核心功能无法使用
- [ ] 高 (High) - 重要功能受影响
- [ ] 中 (Medium) - 功能部分受影响
- [ ] 低 (Low) - 轻微问题或UI问题

## 频率
- [ ] 总是发生
- [ ] 经常发生
- [ ] 偶尔发生
- [ ] 很少发生

## 影响范围
- [ ] 仅影响特定设备/系统
- [ ] 影响所有设备
- [ ] 仅在特定条件下发生

## 临时解决方案
如果您找到了临时解决方案，请描述：

## 附加信息
请添加任何其他相关信息。