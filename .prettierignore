# 依赖目录
node_modules/
.pnpm-store/

# 构建输出
build/
dist/
lib/
coverage/

# 平台特定文件
android/
ios/
.expo/

# 缓存文件
.metro-cache/
.tsbuildinfo
*.tsbuildinfo

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 环境文件
.env*
!.env.example

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# Git文件
.git/
.gitignore

# 特定文件类型
*.min.js
*.min.css
*.bundle.js

# 自动生成的文件
CHANGELOG.md
package-lock.json
yarn.lock
pnpm-lock.yaml

# 文档构建输出
docs/.vuepress/dist/