# 变更日志 (Changelog)

本文件记录了项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 初始化GitHub仓库和GitFlow工作流配置
- 创建项目基础文档和协作规范
- 配置GitHub Actions CI/CD流水线
- 设置分支保护规则和代码审查流程

### 变更
- 无

### 修复
- 无

### 移除
- 无

## [0.1.0] - 2024-01-XX

### 新增
- 项目初始化
- 基础项目结构
- GitFlow工作流配置
- GitHub协作规范

---

## 版本说明

### 版本号格式
- **主版本号 (MAJOR)**: 不兼容的API修改
- **次版本号 (MINOR)**: 向下兼容的功能性新增
- **修订号 (PATCH)**: 向下兼容的问题修正

### 变更类型
- **新增 (Added)**: 新功能
- **变更 (Changed)**: 对现有功能的变更
- **弃用 (Deprecated)**: 即将移除的功能
- **移除 (Removed)**: 已移除的功能
- **修复 (Fixed)**: 任何bug修复
- **安全 (Security)**: 安全相关的修复