# 使用淘宝NPM镜像加速
registry=https://registry.npmmirror.com/

# React Native相关包使用官方源
@react-native:registry=https://registry.npmjs.org/
@react-native-community:registry=https://registry.npmjs.org/

# 其他常用包的镜像配置
sass_binary_site=https://npmmirror.com/mirrors/node-sass/
phantomjs_cdnurl=https://npmmirror.com/mirrors/phantomjs/
electron_mirror=https://npmmirror.com/mirrors/electron/
sqlite3_binary_host_mirror=https://npmmirror.com/mirrors/
profiler_binary_host_mirror=https://npmmirror.com/mirrors/node-inspector/
chromedriver_cdnurl=https://npmmirror.com/mirrors/chromedriver/

# pnpm配置
auto-install-peers=true
strict-peer-dependencies=false
shamefully-hoist=true

# 缓存配置
store-dir=~/.pnpm-store
cache-dir=~/.pnpm-cache

# 安全配置
audit-level=moderate