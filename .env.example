# 应用配置
APP_NAME=SmartSocialEcommerce
APP_VERSION=0.1.0
APP_BUILD_NUMBER=1

# 环境配置
NODE_ENV=development
API_BASE_URL=https://api.example.com
WS_BASE_URL=wss://ws.example.com

# 通义千问API配置
TONGYI_QIANWEN_API_KEY=your_tongyi_api_key_here
TONGYI_QIANWEN_BASE_URL=https://dashscope.aliyuncs.com

# 微信SDK配置
WECHAT_APP_ID=your_wechat_app_id_here
WECHAT_APP_SECRET=your_wechat_app_secret_here
WECHAT_UNIVERSAL_LINK=https://your-domain.com/wechat/

# 支付宝SDK配置
ALIPAY_APP_ID=your_alipay_app_id_here
ALIPAY_PRIVATE_KEY=your_alipay_private_key_here
ALIPAY_PUBLIC_KEY=your_alipay_public_key_here

# 高德地图配置
AMAP_API_KEY_ANDROID=your_amap_android_key_here
AMAP_API_KEY_IOS=your_amap_ios_key_here

# 腾讯云配置
TENCENT_CLOUD_SECRET_ID=your_tencent_secret_id_here
TENCENT_CLOUD_SECRET_KEY=your_tencent_secret_key_here
TENCENT_COS_BUCKET=your_cos_bucket_here
TENCENT_COS_REGION=ap-beijing

# 友盟+配置
UMENG_APP_KEY_ANDROID=your_umeng_android_key_here
UMENG_APP_KEY_IOS=your_umeng_ios_key_here

# 推送服务配置
PUSH_SERVICE_KEY=your_push_service_key_here

# 调试配置
FLIPPER_ENABLED=true
REACTOTRON_ENABLED=true
DEV_MENU_ENABLED=true

# 性能监控
PERFORMANCE_MONITORING_ENABLED=true
CRASH_REPORTING_ENABLED=true

# 安全配置
ENABLE_NETWORK_SECURITY=true
ENABLE_CODE_OBFUSCATION=false
ENABLE_ROOT_DETECTION=true

# 功能开关
ENABLE_AI_FEATURES=true
ENABLE_LIVE_STREAMING=true
ENABLE_SOCIAL_FEATURES=true
ENABLE_PAYMENT_FEATURES=true