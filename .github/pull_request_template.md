## 任务描述
- **任务编号**: Task-{number}
- **任务标题**: {title}
- **相关Issue**: #{issue-number}
- **分支**: `feature/task-{number}-{description}`

## 变更内容
请勾选适用的选项：
- [ ] 新增功能 (feat)
- [ ] Bug修复 (fix)
- [ ] 文档更新 (docs)
- [ ] 代码重构 (refactor)
- [ ] 性能优化 (perf)
- [ ] 测试相关 (test)
- [ ] 构建配置 (chore)

## 功能描述
请详细描述本次PR的功能变更：

### 主要变更
- 

### 技术实现
- 

### 依赖变更
- [ ] 新增依赖包
- [ ] 更新依赖版本
- [ ] 移除依赖包

如有依赖变更，请列出：
- 

## 测试说明
请确认以下测试已完成：
- [ ] 单元测试通过 (`pnpm test`)
- [ ] 类型检查通过 (`pnpm type-check`)
- [ ] 代码规范检查通过 (`pnpm lint`)
- [ ] 格式化检查通过 (`pnpm format:check`)
- [ ] 手动功能测试完成
- [ ] iOS真机测试完成
- [ ] Android真机测试完成

### 测试用例
如有新增测试用例，请说明：
- 

### 测试覆盖率
- 当前覆盖率: _%
- 目标覆盖率: >80%

## 截图/录屏
如果有UI变更，请提供截图或录屏：

### iOS截图
<!-- 请在此处添加iOS截图 -->

### Android截图
<!-- 请在此处添加Android截图 -->

### 功能演示
<!-- 如有必要，请添加功能演示视频或GIF -->

## 性能影响
请评估本次变更对性能的影响：
- [ ] 无性能影响
- [ ] 性能提升
- [ ] 性能下降（请说明原因和优化计划）

### 性能指标
如有性能相关变更，请提供数据：
- 启动时间: 
- 内存使用: 
- 包体积变化: 

## 兼容性检查
- [ ] iOS 12+ 兼容性确认
- [ ] Android 8+ 兼容性确认
- [ ] 新架构(New Architecture)兼容性确认
- [ ] 向后兼容性确认

## 安全检查
- [ ] 无敏感信息泄露
- [ ] API密钥等敏感配置已正确处理
- [ ] 用户数据处理符合隐私政策
- [ ] 第三方SDK安全性确认

## 文档更新
- [ ] README.md已更新
- [ ] API文档已更新
- [ ] 组件文档已更新
- [ ] 变更日志已更新

## 部署说明
- [ ] 无需特殊部署步骤
- [ ] 需要数据库迁移
- [ ] 需要环境变量配置
- [ ] 需要第三方服务配置

如需特殊部署步骤，请说明：
- 

## 检查清单
在提交PR前，请确认以下事项：
- [ ] 代码符合项目规范和最佳实践
- [ ] 已添加必要的单元测试
- [ ] 已更新相关文档
- [ ] 已在真机上测试功能
- [ ] 无TypeScript错误
- [ ] 通过ESLint检查
- [ ] 通过Prettier格式化检查
- [ ] 分支基于最新的develop分支
- [ ] 提交信息符合Conventional Commits规范

## 审查要点
请审查者重点关注：
- 

## 后续计划
- [ ] 无后续工作
- [ ] 需要后续优化（请在Issue中跟踪）
- [ ] 需要相关任务配合

---

**注意**: 
1. 请确保所有检查项都已完成
2. 如有疑问，请在评论中说明
3. 合并后请及时删除功能分支