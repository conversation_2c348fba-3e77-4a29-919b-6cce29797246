# GitHub分支保护规则配置

## 主分支保护 (main)

### 基本设置
- ✅ **Require a pull request before merging**
  - ✅ Require approvals: 1
  - ✅ Dismiss stale PR approvals when new commits are pushed
  - ✅ Require review from code owners

- ✅ **Require status checks to pass before merging**
  - ✅ Require branches to be up to date before merging
  - 必需的状态检查:
    - `lint-and-type-check`
    - `unit-tests`
    - `build-android`
    - `build-ios`
    - `security-scan`

- ✅ **Require conversation resolution before merging**
- ✅ **Require signed commits**
- ✅ **Require linear history**
- ✅ **Include administrators**

### 推送限制
- ✅ **Restrict pushes that create files**
- ✅ **Block force pushes**
- ✅ **Restrict deletions**

## 开发分支保护 (develop)

### 基本设置
- ✅ **Require a pull request before merging**
  - ✅ Require approvals: 1
  - ❌ Dismiss stale PR approvals when new commits are pushed
  - ❌ Require review from code owners

- ✅ **Require status checks to pass before merging**
  - ✅ Require branches to be up to date before merging
  - 必需的状态检查:
    - `lint-and-type-check`
    - `unit-tests`

- ✅ **Require conversation resolution before merging**
- ❌ **Require signed commits**
- ❌ **Require linear history**
- ❌ **Include administrators**

### 推送限制
- ❌ **Restrict pushes that create files**
- ✅ **Block force pushes**
- ❌ **Restrict deletions**

## 发布分支保护 (release/*)

### 基本设置
- ✅ **Require a pull request before merging**
  - ✅ Require approvals: 2
  - ✅ Dismiss stale PR approvals when new commits are pushed
  - ✅ Require review from code owners

- ✅ **Require status checks to pass before merging**
  - ✅ Require branches to be up to date before merging
  - 必需的状态检查:
    - `lint-and-type-check`
    - `unit-tests`
    - `build-android`
    - `build-ios`
    - `security-scan`

## 配置步骤

### 1. 在GitHub仓库中配置
1. 进入仓库 Settings > Branches
2. 点击 "Add rule" 创建分支保护规则
3. 按照上述配置逐一设置

### 2. 设置CODEOWNERS文件
创建 `.github/CODEOWNERS` 文件指定代码审查者

### 3. 配置必需的状态检查
确保GitHub Actions中的job名称与状态检查名称一致

## 注意事项

1. **管理员权限**: 即使是管理员也需要遵循分支保护规则
2. **状态检查**: 所有必需的CI检查都必须通过才能合并
3. **代码审查**: 至少需要一名审查者批准才能合并
4. **冲突解决**: 所有对话必须解决才能合并
5. **签名提交**: 主分支要求签名提交以确保代码完整性